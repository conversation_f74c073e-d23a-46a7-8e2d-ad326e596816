import pandas as pd
from minepy.minepy import MINE
from sklearn.preprocessing import LabelEncoder


def decorator_timer(some_function):
    from time import time

    def wrapper(*args, **kwargs):
        t1 = time()
        result = some_function(*args, **kwargs)
        end = time()-t1
        return result, end
    return wrapper

@decorator_timer
def compute_mic_matrix(df: pd.DataFrame) -> pd.DataFrame:
    """
    Compute the Maximal Information Coefficient (MIC) for all pairs of columns 
    in a mixed-type DataFrame (numeric + categorical).
    
    Parameters
    ----------
    df : pd.DataFrame
        Input dataset with mixed types.
    
    Returns
    -------
    pd.DataFrame
        A symmetric MIC matrix with values in [0, 1].
    """
    # Encode categorical variables as integers
    encoded_df = df.copy()
    for col in encoded_df.columns:
        if encoded_df[col].dtype == "object" or str(encoded_df[col].dtype).startswith("category"):
            encoded_df[col] = LabelEncoder().fit_transform(encoded_df[col].astype(str))
    
    cols = encoded_df.columns
    n = len(cols)
    mic_matrix = pd.DataFrame(0.0, index=cols, columns=cols)
    
    mine = MINE(alpha=0.6, c=15)  # Default parameters from Reshef et al.
    
    for i in range(n):
        for j in range(i, n):
            x = encoded_df.iloc[:, i].values
            y = encoded_df.iloc[:, j].values
            
            mine.compute_score(x, y)
            mic = mine.mic()
            
            mic_matrix.iloc[i, j] = mic
            mic_matrix.iloc[j, i] = mic  # Symmetric
    
    return mic_matrix


data_path  = "./Titanic-Dataset.csv"
df = pd.read_csv(data_path)
mic_matrix = compute_mic_matrix(df)
print(mic_matrix)

